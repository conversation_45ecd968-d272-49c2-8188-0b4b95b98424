<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>状态检测调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>网站状态检测调试</h1>
    
    <div id="debug-info"></div>
    
    <h2>测试结果</h2>
    <div id="test-results"></div>
    
    <script>
    // 显示调试信息
    function showDebugInfo() {
        const debugDiv = document.getElementById('debug-info');
        debugDiv.innerHTML = `
            <h2>调试信息</h2>
            <pre>
当前页面URL: ${window.location.href}
页面路径: ${window.location.pathname}
域名: ${window.location.hostname}
端口: ${window.location.port}
协议: ${window.location.protocol}
            </pre>
        `;
    }
    
    // 测试单个URL
    async function testStatusCheck(testUrl) {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'test-result loading';
        resultDiv.innerHTML = `
            <h3>测试: ${testUrl}</h3>
            <p>状态: 检测中...</p>
        `;
        document.getElementById('test-results').appendChild(resultDiv);
        
        try {
            const apiUrl = `module/status_check.php?url=${encodeURIComponent(testUrl)}`;
            console.log('请求API:', apiUrl);
            
            const response = await fetch(apiUrl);
            console.log('响应状态:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('响应数据:', data);
            
            if (data.error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h3>测试: ${testUrl}</h3>
                    <p>状态: <strong>失败</strong></p>
                    <p>错误: ${data.error}</p>
                    <pre>${JSON.stringify(data.debug, null, 2)}</pre>
                `;
            } else {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `
                    <h3>测试: ${testUrl}</h3>
                    <p>状态: <strong>成功</strong></p>
                    <p>HTTP状态码: ${data.status}</p>
                    <p>服务器: ${data.server || '未知'}</p>
                    <p>响应时间: ${data.response_time}ms</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            }
            
        } catch (error) {
            console.error('测试失败:', error);
            resultDiv.className = 'test-result error';
            resultDiv.innerHTML = `
                <h3>测试: ${testUrl}</h3>
                <p>状态: <strong>异常</strong></p>
                <p>错误: ${error.message}</p>
            `;
        }
    }
    
    // 运行所有测试
    async function runAllTests() {
        showDebugInfo();
        
        const testUrls = [
            'https://www.baidu.com',
            'https://www.bing.com',
            'https://www.google.com',
            'baidu.com',  // 测试无协议URL
            'https://httpbin.org/status/200',
            'https://httpbin.org/status/404',
            'https://nonexistent-domain-12345.com'  // 测试不存在的域名
        ];
        
        for (const url of testUrls) {
            await testStatusCheck(url);
            // 添加延迟避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    // 页面加载后开始测试
    document.addEventListener('DOMContentLoaded', runAllTests);
    </script>
</body>
</html>
