<?php
/**
 * 测试网站状态检测功能
 */

echo "测试网站状态检测功能\n";
echo "====================\n\n";

// 测试CURL功能
function test_curl_function($url) {
    echo "测试CURL功能 - URL: $url\n";

    if (!function_exists('curl_init')) {
        echo "错误: CURL扩展未安装\n";
        return false;
    }

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL            => $url,
        CURLOPT_NOBODY         => true,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HEADER         => true,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_CONNECTTIMEOUT => 3,
        CURLOPT_TIMEOUT        => 6,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT      => 'StatusCheckBot/1.0'
    ]);

    $start_time = microtime(true);
    $head = curl_exec($ch);
    $response_time = (int)round(1000 * (microtime(true) - $start_time));

    if ($head === false) {
        $curl_error = curl_error($ch);
        $curl_errno = curl_errno($ch);
        echo "CURL错误: $curl_error (错误码: $curl_errno)\n";
        curl_close($ch);
        return false;
    }

    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $info = curl_getinfo($ch);
    curl_close($ch);

    echo "HTTP状态码: $code\n";
    echo "响应时间: {$response_time}ms\n";
    echo "连接时间: " . round($info['connect_time'] * 1000) . "ms\n";
    echo "总时间: " . round($info['total_time'] * 1000) . "ms\n";

    return true;
}

// 测试URL列表
$test_urls = [
    'https://www.baidu.com',
    'http://www.baidu.com',
    'https://www.google.com',
    'https://nonexistent-domain-12345.com'
];

foreach ($test_urls as $test_url) {
    echo "\n" . str_repeat("-", 50) . "\n";
    test_curl_function($test_url);
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "测试完成\n";
?>
