<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>链接检测功能最终测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        .link-placeholder { color: #666; font-style: italic; }
        .visit { color: #007bff; text-decoration: none; }
        .visit:hover { text-decoration: underline; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>链接检测功能最终测试</h1>
    
    <div class="test-section">
        <h2>1. 模拟网站目录页面的链接检测</h2>
        <p>以下是模拟的网站列表，应该会自动进行链接检测：</p>
        
        <ul>
            <li>
                <strong>百度</strong> - 中国最大的搜索引擎
                <br>
                <span id="placeholder-1" class="link-placeholder" data-furl="https://www.baidu.com" data-text="www.baidu.com">链接检测中…</span>
            </li>
            <li>
                <strong>必应</strong> - 微软搜索引擎
                <br>
                <span id="placeholder-2" class="link-placeholder" data-furl="https://www.bing.com" data-text="www.bing.com">链接检测中…</span>
            </li>
            <li>
                <strong>谷歌</strong> - 全球最大搜索引擎
                <br>
                <span id="placeholder-3" class="link-placeholder" data-furl="https://www.google.com" data-text="www.google.com">链接检测中…</span>
            </li>
            <li>
                <strong>测试404</strong> - 应该显示404错误
                <br>
                <span id="placeholder-4" class="link-placeholder" data-furl="https://httpbin.org/status/404" data-text="httpbin.org/status/404">链接检测中…</span>
            </li>
            <li>
                <strong>不存在的网站</strong> - 应该显示连接失败
                <br>
                <span id="placeholder-5" class="link-placeholder" data-furl="https://nonexistent-domain-12345.com" data-text="nonexistent-domain-12345.com">链接检测中…</span>
            </li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>2. API直接测试结果</h2>
        <div id="api-test-results">测试中...</div>
    </div>
    
    <div class="test-section">
        <h2>3. 调试信息</h2>
        <div id="debug-info"></div>
    </div>

    <script>
    // 复制webdir.html中的链接检测逻辑
    const STATUS_TEXT = {
        200: '正常200',
        301: '跳转301',
        302: '跳转302',
        403: '禁止访问403',
        404: '未找到404',
        500: '服务器错误500',
        503: '服务不可用503'
    };
    const BLOCK_CODES = [403, 404, 500, 503];
    const CHECK_ENDPOINT = '/module/status_check.php?url=';

    // 工具函数：创建链接
    function makeLink(url, rawText, wid = '') {
        const displayText = (rawText || url)
            .replace(/^https?:\/\//i, '')
            .replace(/\/$/, '');
        
        const a = document.createElement('a');
        a.href = url;
        a.target = '_blank';
        a.className = 'visit';
        a.textContent = displayText;
        return a;
    }

    // 获取状态
    async function fetchStatus(url) {
        try {
            const res = await fetch(CHECK_ENDPOINT + encodeURIComponent(url));
            
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            
            const data = await res.json();
            
            if (data.error) {
                console.error('状态检测API错误:', data.error, data.debug || '');
                throw new Error(data.error);
            }
            
            const code = Number(data.status);
            if (!code) {
                console.error('API返回数据异常:', data);
                throw new Error('接口返回缺少 status 字段');
            }
            
            return code;
        } catch (error) {
            console.error('链接状态检测失败:', {
                url: url,
                endpoint: CHECK_ENDPOINT + encodeURIComponent(url),
                error: error.message
            });
            throw error;
        }
    }

    // 检测占位符
    async function checkPlaceholder(ph) {
        const url = ph.dataset.furl;
        const txt = ph.dataset.text || url;
        const wid = ph.id.startsWith('placeholder-') ? ph.id.slice('placeholder-'.length) : '';

        ph.textContent = '链接检测中…';
        ph.style.color = '#666';

        try {
            const code = await fetchStatus(url);

            if (BLOCK_CODES.includes(code)) {
                ph.textContent = STATUS_TEXT[code] || `异常(${code})`;
                ph.style.color = '#f00';
            } else {
                ph.replaceWith(makeLink(url, txt, wid));
            }

        } catch (err) {
            console.error('链接检测失败:', err);
            ph.textContent = '链接检测失败';
            ph.style.color = '#f00';
        }
    }

    // API直接测试
    async function testApiDirectly() {
        const testUrls = ['https://www.baidu.com', 'https://httpbin.org/status/404'];
        const resultsDiv = document.getElementById('api-test-results');
        let html = '';

        for (const url of testUrls) {
            try {
                const apiUrl = CHECK_ENDPOINT + encodeURIComponent(url);
                const response = await fetch(apiUrl);
                const data = await response.json();

                if (data.error) {
                    html += `<p><strong>${url}</strong>: <span style="color: red;">错误 - ${data.error}</span></p>`;
                } else {
                    html += `<p><strong>${url}</strong>: <span style="color: green;">成功 - 状态码${data.status} (${data.response_time}ms)</span></p>`;
                }
            } catch (error) {
                html += `<p><strong>${url}</strong>: <span style="color: red;">异常 - ${error.message}</span></p>`;
            }
        }

        resultsDiv.innerHTML = html;
    }

    // 显示调试信息
    function showDebugInfo() {
        const debugDiv = document.getElementById('debug-info');
        debugDiv.innerHTML = `
            <pre>
API端点: ${CHECK_ENDPOINT}
当前URL: ${window.location.href}
用户代理: ${navigator.userAgent}
            </pre>
        `;
    }

    // 页面加载后执行
    document.addEventListener('DOMContentLoaded', () => {
        showDebugInfo();
        testApiDirectly();
        
        // 检测所有占位符
        document.querySelectorAll('.link-placeholder').forEach(checkPlaceholder);
    });
    </script>
</body>
</html>
