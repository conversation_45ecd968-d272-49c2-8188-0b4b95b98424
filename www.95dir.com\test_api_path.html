<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>API路径测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API路径测试</h1>
    
    <div id="path-info"></div>
    <div id="test-results"></div>
    
    <script>
    // 显示路径信息
    function showPathInfo() {
        const pathDiv = document.getElementById('path-info');
        const currentPath = window.location.pathname;
        const basePath = currentPath.replace(/\/[^\/]*$/, '/');
        
        pathDiv.innerHTML = `
            <h2>路径信息</h2>
            <pre>
当前完整URL: ${window.location.href}
当前路径: ${currentPath}
基础路径: ${basePath}
API相对路径1: module/status_check.php
API相对路径2: ./module/status_check.php
API绝对路径: ${window.location.origin}${basePath}module/status_check.php
            </pre>
        `;
    }
    
    // 测试不同的API路径
    async function testApiPaths() {
        const testUrl = 'https://www.baidu.com';
        const basePath = window.location.pathname.replace(/\/[^\/]*$/, '/');
        
        const apiPaths = [
            'module/status_check.php',
            './module/status_check.php',
            `${basePath}module/status_check.php`,
            `/95分类目录/www.95dir.com/module/status_check.php`,
            `${window.location.origin}${basePath}module/status_check.php`
        ];
        
        const resultsDiv = document.getElementById('test-results');
        resultsDiv.innerHTML = '<h2>API路径测试结果</h2>';
        
        for (let i = 0; i < apiPaths.length; i++) {
            const apiPath = apiPaths[i];
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result';
            
            try {
                const fullUrl = `${apiPath}?url=${encodeURIComponent(testUrl)}`;
                console.log(`测试路径 ${i + 1}:`, fullUrl);
                
                resultDiv.innerHTML = `
                    <h3>测试路径 ${i + 1}</h3>
                    <p>API路径: <code>${apiPath}</code></p>
                    <p>完整URL: <code>${fullUrl}</code></p>
                    <p>状态: 请求中...</p>
                `;
                resultsDiv.appendChild(resultDiv);
                
                const response = await fetch(fullUrl);
                const data = await response.json();
                
                if (data.error) {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML += `
                        <p>结果: <strong>失败</strong></p>
                        <p>错误: ${data.error}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML += `
                        <p>结果: <strong>成功</strong></p>
                        <p>状态码: ${data.status}</p>
                        <p>响应时间: ${data.response_time}ms</p>
                    `;
                }
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML += `
                    <p>结果: <strong>网络错误</strong></p>
                    <p>错误: ${error.message}</p>
                `;
            }
            
            // 添加延迟避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    
    // 页面加载后开始测试
    document.addEventListener('DOMContentLoaded', () => {
        showPathInfo();
        testApiPaths();
    });
    </script>
</body>
</html>
