<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单链接检测测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .link-placeholder { color: #666; font-style: italic; }
        a.visit { color: #008000; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>简单链接检测测试</h1>
    
    <div class="test-item">
        <h3>测试网站1：百度</h3>
        <p>链接状态：
            <span id="placeholder-1" 
                  class="link-placeholder" 
                  data-furl="https://www.baidu.com" 
                  data-text="www.baidu.com" 
                  style="color:#666;">
                链接检测中…
            </span>
        </p>
    </div>
    
    <div class="test-item">
        <h3>测试网站2：必应</h3>
        <p>链接状态：
            <span id="placeholder-2" 
                  class="link-placeholder" 
                  data-furl="https://www.bing.com" 
                  data-text="www.bing.com" 
                  style="color:#666;">
                链接检测中…
            </span>
        </p>
    </div>
    
    <div class="test-item">
        <h3>测试网站3：404错误</h3>
        <p>链接状态：
            <span id="placeholder-3" 
                  class="link-placeholder" 
                  data-furl="https://httpbin.org/status/404" 
                  data-text="httpbin.org/status/404" 
                  style="color:#666;">
                链接检测中…
            </span>
        </p>
    </div>
    
    <div class="test-item">
        <h3>调试信息</h3>
        <div id="debug-output"></div>
    </div>

    <script>
    // 完全复制您提供的正确代码
    document.addEventListener('DOMContentLoaded', () => {

      /* ---- 配置区 ---- */
      const STATUS_TEXT = {
        0: '连接失败',
        200: '正常200',
        301: '跳转301',
        302: '跳转302',
        403: '禁止访问403',
        404: '未找到404',
        500: '服务器错误500',
        503: '服务不可用503'
      };
      const BLOCK_CODES     = [0, 403, 404, 500, 503]; // 这些状态隐藏链接
      const CHECK_INTERVAL  = 300000;               // 5 分钟重检一次
      const CHECK_ENDPOINT  = '/module/status_check.php?url=';

      /* ---- 工具函数 ---- */
    function makeLink(url, rawText, wid = '') {
      /* ① 显示文字：先去协议，再去尾部 "/" */
      const displayText = (rawText || url)
                            .replace(/^https?:\/\//i, '') // 去掉 http:// 或 https://
                            .replace(/\/$/, '');          // 如果最后还有 / 就去掉

      /* ② 生成链接 */
      const a = document.createElement('a');
      a.href        = url;          // href 仍保留协议
      a.target      = '_blank';
      a.className   = 'visit';
      a.textContent = displayText;  // 用户看到的文本
      if (wid && typeof clickout === 'function') {
        a.addEventListener('click', () => clickout(wid));
      }
      return a;
    }

      async function fetchStatus(url) {
        const debugDiv = document.getElementById('debug-output');
        
        try {
          debugDiv.innerHTML += `<p>开始检测: ${url}</p>`;
          debugDiv.innerHTML += `<p>API端点: ${CHECK_ENDPOINT + encodeURIComponent(url)}</p>`;
          
          const res = await fetch(CHECK_ENDPOINT + encodeURIComponent(url));
          debugDiv.innerHTML += `<p>Fetch响应: ${res.status} ${res.statusText}</p>`;

          if (!res.ok) {
            throw new Error(`HTTP ${res.status}: ${res.statusText}`);
          }

          const data = await res.json();
          debugDiv.innerHTML += `<p>API返回数据: <pre>${JSON.stringify(data, null, 2)}</pre></p>`;

          if (data.error) {
            console.error('状态检测API错误:', data.error, data.debug || '');
            throw new Error(data.error);
          }

          const code = Number(data.status);
          debugDiv.innerHTML += `<p>解析状态码: ${code} (原始: ${data.status})</p>`;

          // 注意：状态码0是有效的（表示连接失败），只有undefined/null才是异常
          if (data.status === undefined || data.status === null) {
            console.error('API返回数据异常:', data);
            throw new Error('接口返回缺少 status 字段');
          }

          return code;
        } catch (error) {
          debugDiv.innerHTML += `<p style="color: red;">错误: ${error.message}</p>`;
          console.error('链接状态检测失败:', {
            url: url,
            endpoint: CHECK_ENDPOINT + encodeURIComponent(url),
            error: error.message
          });
          throw error;
        }
      }

      /* ---- 检测：列表页 placeholder ---- */
      async function checkPlaceholder(ph) {
        const url = ph.dataset.furl;
        const txt = ph.dataset.text || url;               // 兜底：无 data-text 时用 URL
        const wid = ph.id.startsWith('placeholder-')
                    ? ph.id.slice('placeholder-'.length)
                    : '';

        // 初始提示
        ph.textContent = '链接检测中…';
        ph.style.color = '#666';

        try {
          const code = await fetchStatus(url);

          if (BLOCK_CODES.includes(code)) {
            // 403/404/500/503 &rarr; 红字提示，不显示链接
            ph.textContent = STATUS_TEXT[code] || `异常(${code})`;
            ph.style.color = '#f00';
          } else {
            // 正常 &rarr; 用 <a> 替换占位
            ph.replaceWith( makeLink(url, txt, wid) );
          }

        } catch (err) {
          // 网络 / JSON / CORS 等错误
          console.error('链接检测失败:', err);
          ph.textContent = '链接检测失败';
          ph.style.color = '#f00';
        }
      }

      /* ---- 批量执行 ---- */
      function runBatch() {
        document.querySelectorAll('.link-placeholder').forEach(checkPlaceholder);
      }

      /* ---- 启动 ---- */
      runBatch();                       // 页面加载立即检测
    });
    </script>
</body>
</html>
