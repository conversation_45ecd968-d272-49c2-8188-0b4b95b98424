<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="arcbox" class="clearfix">
				<h3>数据归档</h3>
				<ul class="arclist">
					{#foreach from=get_archives() key=year item=arr#}
                	<li>
                		<h4>{#$year#}年</h4>
                    	<p>
                    		{#foreach from=$arr key=month item=item#}
                    		<a href="{#$item.arc_link#}" title="{#$year#}年{#$month#}月共有{#$item.site_count#}个站点">{#$month#}月</a>
                        	{#/foreach#}
                    	</p>
                	</li>
					{#/foreach#}
				</ul>
			</div>
            <div class="blank10"></div>
            <div id="listbox" class="clearfix">
            	<h2>{#$pagename#}</h2>
            	<ul class="sitelist">
					{#foreach from=$websites item=w name=list#}
                	<li><a href="{#$w.web_link#}"><img src="{#$w.web_pic#}" width="100" height="80" alt="{#$w.web_name#}" class="thumb" /></a><div class="info"><h3><a href="{#$w.web_link#}" title="{#$w.web_name#}">{#$w.web_name#}</a> {#if $w.is_today#}<span class="new-icon">new</span>{#/if#} {#if $w.web_ispay == 1#}<img src="{#$site_root#}public/images/attr/audit.gif" border="0">{#/if#} {#if $w.web_istop == 1#}<img src="{#$site_root#}public/images/attr/top.gif" border="0">{#/if#} {#if $w.web_isbest == 1#}<img src="{#$site_root#}public/images/attr/best.gif" border="0">{#/if#}</h3><p>{#$w.web_intro#}</p><address>
  <!-- 仅占位，没有 <a> -->
  <span id="placeholder-{#$w.web_id#}"
        class="link-placeholder"
        data-furl="{#$w.web_furl#}"
        data-text="{#$w.web_url#}">
        链接检测中…
  </span>
  - {#$w.web_ctime#} -
  <a href="javascript:;" class="addfav"
     onClick="addfav({#$w.web_id#})" title="点击收藏">收藏</a>
</address></div></li>
                	{#foreachelse#}
                	<li>该目录下无任何内容！</li>
                	{#/foreach#}
				</ul>
            	<div class="showpage">{#$showpage#}</div>
            </div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>-->

            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 10, true) item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$best.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
            
            <div class="blank10"></div>
            
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

<script>
/* ========= 列表占位网址检测 ========= */
(() => {
  /* 基本配置 */
  // 智能检测API路径
  let API;
  if (window.location.protocol === 'file:') {
    // 如果是file协议，使用绝对路径（用于测试）
    API = 'http://localhost/95分类目录/www.95dir.com/module/status_check.php?url=';
  } else {
    // 如果是http/https协议，使用相对路径
    API = 'module/status_check.php?url=';
  }

  // 调试信息
  console.log('API endpoint:', API);
  console.log('当前协议:', window.location.protocol);
  console.log('当前页面路径:', window.location.pathname);
  const INTERVAL = 300000;                                 // 5 分钟重检
  const MAP   = {200:'正常200',301:'跳转301',302:'跳转302',
                 403:'禁止访问403',404:'未找到404',
                 500:'服务器错误500',503:'服务不可用503'};
  const HIDE  = [403,404,500,503];                         // 隐藏链接状态码

  /* 工具函数 */
  const strip = u => u.replace(/^https?:\/\//i,'').replace(/\/$/,'');
  const makeA = (url,text,wid) => {
    const a = document.createElement('a');
    a.href = url; a.target = '_blank'; a.className = 'visit';
    a.textContent = strip(text||url);
    if (wid && typeof clickout==='function') a.onclick = () => clickout(wid);
    return a;
  };
  const getStatus = async url =>
    Number((await (await fetch(API+encodeURIComponent(url))).json()).status);

  /* 单条检测 */
  async function process(span){
    const url = span.dataset.furl;
    const txt = span.dataset.text || url;
    const wid = span.id.replace(/^placeholder-/,'');
    span.textContent = '链接检测中…';
    span.style.color = '#666';

    try{
      const code = await getStatus(url);
      if (HIDE.includes(code)){
        span.textContent = MAP[code] || `异常(${code})`;
        span.style.color = '#f00';
      }else{
        span.replaceWith( makeA(url,txt,wid) );
      }
    }catch{
      span.textContent = '链接检测失败';
      span.style.color = '#f00';
    }
  }

  /* 扫描全部占位并定时重检 */
  const scan = () => document
      .querySelectorAll('.link-placeholder')
      .forEach(process);

  document.addEventListener('DOMContentLoaded', () => {
    /* 绿色链接样式 */
    const st = document.createElement('style');
    st.textContent = 'a.visit{color:#008000;}';
    document.head.appendChild(st);

    scan();                       // 首次检测
    setInterval(scan, INTERVAL);  // 定时重检
  });
})();
</script>

</body>
</html>