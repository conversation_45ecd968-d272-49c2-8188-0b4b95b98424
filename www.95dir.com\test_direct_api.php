<?php
/**
 * 直接测试status_check.php API
 */
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>直接API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>直接API测试</h1>
    
    <?php
    // 测试URL列表
    $testUrls = [
        'https://www.baidu.com',
        'https://www.bing.com',
        'https://www.google.com',
        'baidu.com',
        'https://httpbin.org/status/200',
        'https://httpbin.org/status/404'
    ];
    
    echo "<h2>服务器端测试结果</h2>";
    
    foreach ($testUrls as $testUrl) {
        echo "<div class='test-result'>";
        echo "<h3>测试URL: " . htmlspecialchars($testUrl) . "</h3>";
        
        // 构建API URL
        $apiUrl = "module/status_check.php?url=" . urlencode($testUrl);
        echo "<p>API URL: <code>" . htmlspecialchars($apiUrl) . "</code></p>";
        
        // 使用file_get_contents获取API响应
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Mozilla/5.0 (compatible; TestBot/1.0)'
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        
        if ($response === false) {
            echo "<p class='error'>请求失败：无法获取API响应</p>";
        } else {
            $data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($data['error'])) {
                    echo "<p class='error'>API错误：" . htmlspecialchars($data['error']) . "</p>";
                    if (isset($data['debug'])) {
                        echo "<pre>" . htmlspecialchars(json_encode($data['debug'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
                    }
                } else {
                    echo "<p class='success'>成功！</p>";
                    echo "<p>状态码: " . (isset($data['status']) ? $data['status'] : '未知') . "</p>";
                    echo "<p>响应时间: " . (isset($data['response_time']) ? $data['response_time'] . 'ms' : '未知') . "</p>";
                    if (isset($data['server'])) {
                        echo "<p>服务器: " . htmlspecialchars($data['server']) . "</p>";
                    }
                }
            } else {
                echo "<p class='error'>JSON解析失败</p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            }
        }
        
        echo "</div>";
        
        // 添加延迟避免请求过于频繁
        usleep(500000); // 0.5秒
    }
    ?>
    
    <h2>前端JavaScript测试</h2>
    <div id="js-test-results"></div>
    
    <script>
    // 前端JavaScript测试
    async function testJavaScriptApi() {
        const testUrls = [
            'https://www.baidu.com',
            'https://www.bing.com'
        ];
        
        const resultsDiv = document.getElementById('js-test-results');
        
        for (const testUrl of testUrls) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result';
            resultDiv.innerHTML = `
                <h3>JS测试: ${testUrl}</h3>
                <p>状态: 测试中...</p>
            `;
            resultsDiv.appendChild(resultDiv);
            
            try {
                const apiUrl = `module/status_check.php?url=${encodeURIComponent(testUrl)}`;
                console.log('JS请求API:', apiUrl);
                
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                if (data.error) {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <h3>JS测试: ${testUrl}</h3>
                        <p>状态: <strong>失败</strong></p>
                        <p>错误: ${data.error}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <h3>JS测试: ${testUrl}</h3>
                        <p>状态: <strong>成功</strong></p>
                        <p>状态码: ${data.status}</p>
                        <p>响应时间: ${data.response_time}ms</p>
                    `;
                }
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h3>JS测试: ${testUrl}</h3>
                    <p>状态: <strong>异常</strong></p>
                    <p>错误: ${error.message}</p>
                `;
            }
            
            // 添加延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    // 页面加载后开始JavaScript测试
    document.addEventListener('DOMContentLoaded', testJavaScriptApi);
    </script>
</body>
</html>
