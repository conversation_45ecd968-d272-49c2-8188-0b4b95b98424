<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>链接检测测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .result { margin-top: 5px; font-weight: bold; }
        .error { color: red; }
        .success { color: green; }
        .loading { color: #666; }
    </style>
</head>
<body>
    <h1>链接检测功能测试</h1>
    
    <div id="test-results"></div>
    
    <script>
    // 测试URL列表
    const testUrls = [
        'https://www.baidu.com',
        'https://www.google.com',
        'https://www.bing.com',
        'https://httpbin.org/status/200',
        'https://httpbin.org/status/404',
        'https://nonexistent-domain-12345.com',
        'baidu.com'
    ];
    
    // 状态码映射
    const STATUS_TEXT = {
        200: '正常200',
        301: '跳转301',
        302: '跳转302',
        403: '禁止访问403',
        404: '未找到404',
        500: '服务器错误500',
        503: '服务不可用503'
    };
    
    // 检测单个URL
    async function testUrl(url) {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'test-item';
        resultDiv.innerHTML = `
            <div>测试URL: ${url}</div>
            <div class="result loading">检测中...</div>
        `;
        document.getElementById('test-results').appendChild(resultDiv);
        
        const resultElement = resultDiv.querySelector('.result');
        
        try {
            // 构建API URL
            const apiUrl = `module/status_check.php?url=${encodeURIComponent(url)}`;
            console.log('请求URL:', apiUrl);
            
            const response = await fetch(apiUrl);
            console.log('响应状态:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('响应数据:', data);
            
            if (data.error) {
                resultElement.className = 'result error';
                resultElement.innerHTML = `错误: ${data.error}`;
                if (data.debug) {
                    resultElement.innerHTML += `<br>调试信息: ${JSON.stringify(data.debug)}`;
                }
            } else if (data.status) {
                const statusText = STATUS_TEXT[data.status] || `状态码${data.status}`;
                resultElement.className = 'result success';
                resultElement.innerHTML = `${statusText} (${data.response_time}ms)`;
                if (data.server) {
                    resultElement.innerHTML += `<br>服务器: ${data.server}`;
                }
            } else {
                resultElement.className = 'result error';
                resultElement.innerHTML = '响应格式错误: 缺少status字段';
            }
            
        } catch (error) {
            console.error('检测失败:', error);
            resultElement.className = 'result error';
            resultElement.innerHTML = `检测失败: ${error.message}`;
        }
    }
    
    // 开始测试
    async function runTests() {
        document.getElementById('test-results').innerHTML = '<h2>开始测试...</h2>';
        
        for (const url of testUrls) {
            await testUrl(url);
            // 添加延迟避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('所有测试完成');
    }
    
    // 页面加载后开始测试
    document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
